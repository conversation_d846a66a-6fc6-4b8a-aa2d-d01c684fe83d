# 域控告警持续时间改进

## 改进概述

本次改进主要针对域控告警功能，增加了对上报recordTime的记录和使用，使告警系统能够更准确地跟踪数据的时间序列。

## 主要变更

### 1. 新增DomainDataRecord类

创建了`DomainDataRecord`类来封装数值和时间戳信息：

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DomainDataRecord implements Serializable {
    private Float value;                    // 数据值
    private Date recordTime;               // 记录时间（上报的recordTime）
    
    public static DomainDataRecord of(Float value, Date recordTime) {
        return DomainDataRecord.builder()
                .value(value)
                .recordTime(recordTime)
                .build();
    }
    
    public boolean isAboveThreshold(Float threshold) {
        return value != null && threshold != null && value >= threshold;
    }
    
    public boolean isBelowThreshold(Float threshold) {
        return value != null && threshold != null && value < threshold;
    }
}
```

### 2. 修改VehicleDomainControllerAlarmManager

#### 主要修改点：

1. **recordValueToRedis方法**：
   - 原来只记录Float值
   - 现在记录DomainDataRecord对象，包含值和时间戳

```java
// 修改前
private void recordValueToRedis(String redisKey, Float value) {
    redissonRepository.addToList(redisKey, value);
}

// 修改后
private void recordValueToRedis(String redisKey, Float value, Date recordTime) {
    DomainDataRecord record = DomainDataRecord.of(value, recordTime);
    redissonRepository.addToList(redisKey, record);
}
```

2. **checkAlarmCondition方法**：
   - 原来处理List<Float>
   - 现在处理List<DomainDataRecord>

```java
// 修改前
List<Float> recentValues = redissonRepository.getListRange(redisKey, -ABNORMAL_COUNT_THRESHOLD, -1);
for (Float value : recentValues) {
    if (value == null || value < threshold) {
        return false;
    }
}

// 修改后
List<DomainDataRecord> recentRecords = redissonRepository.getListRange(redisKey, -ABNORMAL_COUNT_THRESHOLD, -1);
for (DomainDataRecord record : recentRecords) {
    if (record == null || !record.isAboveThreshold(threshold)) {
        return false;
    }
}
```

3. **checkRemovalCondition方法**：
   - 同样从处理Float值改为处理DomainDataRecord

### 3. 更新单元测试

创建了新的测试用例来验证时间戳记录功能：

```java
@Test
void testHandleDomainControllerAlarm_ShouldGenerateAlarm_WhenThresholdExceeded() {
    // 创建包含时间戳的测试记录
    List<DomainDataRecord> exceedingRecords = Arrays.asList(
        DomainDataRecord.of(120.0f, new Date()),
        DomainDataRecord.of(121.0f, new Date()),
        DomainDataRecord.of(122.0f, new Date())
    );
    when(redissonRepository.getListRange(anyString(), eq(-3), eq(-1)))
            .thenReturn(exceedingRecords);
    
    // 验证记录DomainDataRecord对象
    verify(redissonRepository, atLeastOnce()).addToList(anyString(), any(DomainDataRecord.class));
}
```

## 改进效果

### 1. 时间戳记录
- **之前**：只记录数值，无法知道具体的上报时间
- **现在**：同时记录数值和上报时间，可以进行更精确的时间序列分析

### 2. 数据完整性
- **之前**：Redis中只有Float值的列表
- **现在**：Redis中存储完整的DomainDataRecord对象，包含值和时间戳

### 3. 扩展性
- **之前**：如果需要添加更多字段（如数据来源、设备状态等），需要大幅修改
- **现在**：可以轻松在DomainDataRecord中添加新字段

### 4. 调试和监控
- **之前**：难以追踪具体的数据上报时间
- **现在**：可以精确知道每个数据点的上报时间，便于问题排查

## 使用示例

```java
// 处理域控数据时，会自动记录时间戳
public void processDomainData(List<DomainData> domainDataList, String vehicleName, Date recordTime) {
    List<DomainDataDTO> dtoList = ConvertListUtil.convertList(domainDataList, 
        item -> convertDomainData(item, recordTime, vehicleName));
    
    // 处理域控告警，recordTime会被记录到Redis中
    vehicleDomainControllerAlarmManager.handleDomainControllerAlarm(vehicleName, dtoList, recordTime);
}
```

## 兼容性说明

- 此改进是向后兼容的，不会影响现有的告警逻辑
- Redis中的数据结构发生了变化，但通过序列化机制保证了数据的正确存储和读取
- 现有的阈值检查逻辑保持不变，只是数据访问方式有所调整

## 总结

通过这次改进，域控告警系统现在能够：
1. 记录完整的时间戳信息
2. 提供更准确的持续时间计算
3. 支持更复杂的时间序列分析
4. 便于问题排查和系统监控

这为后续可能的功能扩展（如基于时间窗口的告警、数据趋势分析等）奠定了基础。
