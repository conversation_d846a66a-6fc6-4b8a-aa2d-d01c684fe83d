/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.server.business.manager;

import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.hardware.data.DomainDataDTO;
import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import com.jdx.rover.server.business.manager.domain.DomainDataRecord;
import com.jdx.rover.server.business.manager.report.VehicleAlarmManager;
import com.jdx.rover.server.repository.redis.RedissonRepository;
import com.jdx.rover.server.repository.redis.guardian.ReportAlarmRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 域控告警管理器测试（基于持续时间）
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-03
 */
@ExtendWith(MockitoExtension.class)
class VehicleDomainControllerAlarmManagerTest {

    @Mock
    private VehicleAlarmManager vehicleAlarmManager;

    @Mock
    private ReportAlarmRepository reportAlarmRepository;

    @Mock
    private RedissonRepository redissonRepository;

    @InjectMocks
    private VehicleDomainControllerAlarmManager alarmManager;

    private String vehicleName;
    private Date recordTime;
    private List<DomainDataDTO> domainDataList;

    @BeforeEach
    void setUp() {
        vehicleName = "TEST_VEHICLE_001";
        recordTime = new Date();
        
        // 创建测试数据
        DomainDataDTO domainData = DomainDataDTO.builder()
                .vehicleName(vehicleName)
                .socTemperature(120.0f)  // 超过115度阈值
                .cpuLoad(95.0f)          // 超过90%阈值
                .memoryUsageRate(85.0f)  // 未超过90%阈值
                .diskUsageRate(95.0f)    // 超过90%阈值
                .diskTemperature(75.0f)  // 超过70度阈值
                .deviceId(1)
                .recordTime(recordTime)
                .build();
        
        domainDataList = Arrays.asList(domainData);
    }

    @Test
    void testHandleDomainControllerAlarm_ShouldGenerateAlarm_WhenDurationExceeded() {
        // Given
        Map<String, VehicleAlarmEventDTO> alarmMapDb = new HashMap<>();
        when(reportAlarmRepository.get(vehicleName)).thenReturn(alarmMapDb);
        
        // 模拟Redis List大小和数据
        when(redissonRepository.getListSize(anyString())).thenReturn(3);
        
        // 创建持续超过阈值的记录（持续时间超过60秒）
        Date baseTime = new Date();
        List<DomainDataRecord> exceedingRecords = Arrays.asList(
            DomainDataRecord.of(120.0f, new Date(baseTime.getTime())),
            DomainDataRecord.of(121.0f, new Date(baseTime.getTime() + 30000)), // 30秒后
            DomainDataRecord.of(122.0f, new Date(baseTime.getTime() + 65000))  // 65秒后，超过60秒阈值
        );
        when(redissonRepository.getListRange(anyString(), eq(0), eq(-1)))
                .thenReturn(exceedingRecords);
        
        // When
        alarmManager.handleDomainControllerAlarm(vehicleName, domainDataList, recordTime);
        
        // Then
        verify(vehicleAlarmManager, times(1)).addAlarm(eq(vehicleName), any(VehicleAlarmEventDTO.class));
        verify(redissonRepository, atLeastOnce()).addToList(anyString(), any(DomainDataRecord.class));
    }

    @Test
    void testHandleDomainControllerAlarm_ShouldNotGenerateAlarm_WhenDurationNotExceeded() {
        // Given
        Map<String, VehicleAlarmEventDTO> alarmMapDb = new HashMap<>();
        when(reportAlarmRepository.get(vehicleName)).thenReturn(alarmMapDb);
        
        // 模拟Redis List大小和数据
        when(redissonRepository.getListSize(anyString())).thenReturn(3);
        
        // 创建持续时间不足60秒的记录
        Date baseTime = new Date();
        List<DomainDataRecord> shortDurationRecords = Arrays.asList(
            DomainDataRecord.of(120.0f, new Date(baseTime.getTime())),
            DomainDataRecord.of(121.0f, new Date(baseTime.getTime() + 20000)), // 20秒后
            DomainDataRecord.of(122.0f, new Date(baseTime.getTime() + 40000))  // 40秒后，未超过60秒阈值
        );
        when(redissonRepository.getListRange(anyString(), eq(0), eq(-1)))
                .thenReturn(shortDurationRecords);
        
        // When
        alarmManager.handleDomainControllerAlarm(vehicleName, domainDataList, recordTime);
        
        // Then
        verify(vehicleAlarmManager, never()).addAlarm(eq(vehicleName), any(VehicleAlarmEventDTO.class));
    }

    @Test
    void testHandleDomainControllerAlarm_ShouldNotGenerateAlarm_WhenValuesIntermittent() {
        // Given
        Map<String, VehicleAlarmEventDTO> alarmMapDb = new HashMap<>();
        when(reportAlarmRepository.get(vehicleName)).thenReturn(alarmMapDb);
        
        // 模拟Redis List大小和数据
        when(redissonRepository.getListSize(anyString())).thenReturn(4);
        
        // 创建间歇性超过阈值的记录（中间有正常值）
        Date baseTime = new Date();
        List<DomainDataRecord> intermittentRecords = Arrays.asList(
            DomainDataRecord.of(120.0f, new Date(baseTime.getTime())),
            DomainDataRecord.of(110.0f, new Date(baseTime.getTime() + 30000)), // 30秒后，正常值
            DomainDataRecord.of(125.0f, new Date(baseTime.getTime() + 60000)), // 60秒后，异常值
            DomainDataRecord.of(130.0f, new Date(baseTime.getTime() + 90000))  // 90秒后，异常值
        );
        when(redissonRepository.getListRange(anyString(), eq(0), eq(-1)))
                .thenReturn(intermittentRecords);
        
        // When
        alarmManager.handleDomainControllerAlarm(vehicleName, domainDataList, recordTime);
        
        // Then
        verify(vehicleAlarmManager, never()).addAlarm(eq(vehicleName), any(VehicleAlarmEventDTO.class));
    }

    @Test
    void testHandleDomainControllerAlarm_ShouldRemoveAlarm_WhenNormalDurationExceeded() {
        // Given
        Map<String, VehicleAlarmEventDTO> alarmMapDb = new HashMap<>();
        VehicleAlarmEventDTO existingAlarm = new VehicleAlarmEventDTO();
        existingAlarm.setType(AlarmTypeEnum.DOMAIN_CONTROLLER_ALARM.getValue());
        alarmMapDb.put(AlarmTypeEnum.DOMAIN_CONTROLLER_ALARM.getValue(), existingAlarm);
        
        when(reportAlarmRepository.get(vehicleName)).thenReturn(alarmMapDb);
        when(redissonRepository.getListSize(anyString())).thenReturn(3);
        
        // 创建持续正常的记录（持续时间超过15秒）
        Date baseTime = new Date();
        List<DomainDataRecord> normalRecords = Arrays.asList(
            DomainDataRecord.of(80.0f, new Date(baseTime.getTime())),
            DomainDataRecord.of(85.0f, new Date(baseTime.getTime() + 10000)), // 10秒后
            DomainDataRecord.of(88.0f, new Date(baseTime.getTime() + 20000))  // 20秒后，超过15秒阈值
        );
        when(redissonRepository.getListRange(anyString(), eq(0), eq(-1)))
                .thenReturn(normalRecords);
        
        // 创建正常的域控数据
        DomainDataDTO normalData = DomainDataDTO.builder()
                .vehicleName(vehicleName)
                .socTemperature(100.0f)  // 低于115度阈值
                .cpuLoad(80.0f)          // 低于90%阈值
                .memoryUsageRate(80.0f)  // 低于90%阈值
                .diskUsageRate(80.0f)    // 低于90%阈值
                .diskTemperature(60.0f)  // 低于70度阈值
                .deviceId(1)
                .recordTime(recordTime)
                .build();
        
        List<DomainDataDTO> normalDataList = Arrays.asList(normalData);
        
        // When
        alarmManager.handleDomainControllerAlarm(vehicleName, normalDataList, recordTime);
        
        // Then
        verify(vehicleAlarmManager, times(1)).removeAlarm(vehicleName, AlarmTypeEnum.DOMAIN_CONTROLLER_ALARM.getValue());
    }

    @Test
    void testDomainDataRecord_TimeFunctions() {
        // Given
        Date time1 = new Date();
        Date time2 = new Date(time1.getTime() + 30000); // 30秒后
        
        DomainDataRecord record1 = DomainDataRecord.of(120.0f, time1);
        DomainDataRecord record2 = DomainDataRecord.of(125.0f, time2);
        
        // Then
        assert record1.getTimeDifferenceMs(record2) == 30000L;
        assert record1.isBefore(time2);
        assert record2.isAfter(time1);
        assert record1.isAboveThreshold(115.0f);
        assert record1.isBelowThreshold(125.0f);
    }

    @Test
    void testHandleDomainControllerAlarm_ShouldHandleEmptyList() {
        // Given
        List<DomainDataDTO> emptyList = Collections.emptyList();
        
        // When
        alarmManager.handleDomainControllerAlarm(vehicleName, emptyList, recordTime);
        
        // Then
        verify(reportAlarmRepository, never()).get(anyString());
        verify(vehicleAlarmManager, never()).addAlarm(anyString(), any(VehicleAlarmEventDTO.class));
        verify(vehicleAlarmManager, never()).removeAlarm(anyString(), anyString());
    }

    @Test
    void testHandleDomainControllerAlarm_ShouldHandleNullValues() {
        // Given
        DomainDataDTO dataWithNulls = DomainDataDTO.builder()
                .vehicleName(vehicleName)
                .socTemperature(null)    // null值
                .cpuLoad(95.0f)
                .memoryUsageRate(null)   // null值
                .diskUsageRate(95.0f)
                .diskTemperature(null)   // null值
                .deviceId(1)
                .recordTime(recordTime)
                .build();
        
        List<DomainDataDTO> dataListWithNulls = Arrays.asList(dataWithNulls);
        Map<String, VehicleAlarmEventDTO> alarmMapDb = new HashMap<>();
        when(reportAlarmRepository.get(vehicleName)).thenReturn(alarmMapDb);
        
        // When
        alarmManager.handleDomainControllerAlarm(vehicleName, dataListWithNulls, recordTime);
        
        // Then
        // 应该正常处理，不抛出异常
        // 只有非null值会被记录
        verify(redissonRepository, atLeastOnce()).addToList(anyString(), any(DomainDataRecord.class));
    }
}
