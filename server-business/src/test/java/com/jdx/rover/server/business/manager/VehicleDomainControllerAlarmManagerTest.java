/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.server.business.manager;

import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.hardware.data.DomainDataDTO;
import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import com.jdx.rover.server.business.manager.report.VehicleAlarmManager;
import com.jdx.rover.server.repository.redis.RedissonRepository;
import com.jdx.rover.server.repository.redis.guardian.ReportAlarmRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 域控告警管理器测试
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-03
 */
@ExtendWith(MockitoExtension.class)
class VehicleDomainControllerAlarmManagerTest {

    @Mock
    private VehicleAlarmManager vehicleAlarmManager;

    @Mock
    private ReportAlarmRepository reportAlarmRepository;

    @Mock
    private RedissonRepository redissonRepository;

    @InjectMocks
    private VehicleDomainControllerAlarmManager alarmManager;

    private String vehicleName;
    private Date recordTime;
    private List<DomainDataDTO> domainDataList;

    @BeforeEach
    void setUp() {
        vehicleName = "TEST_VEHICLE_001";
        recordTime = new Date();
        
        // 创建测试数据
        DomainDataDTO domainData = DomainDataDTO.builder()
                .vehicleName(vehicleName)
                .socTemperature(120.0f)  // 超过115度阈值
                .cpuLoad(95.0f)          // 超过90%阈值
                .memoryUsageRate(85.0f)  // 未超过90%阈值
                .diskUsageRate(95.0f)    // 超过90%阈值
                .diskTemperature(75.0f)  // 超过70度阈值
                .deviceId(1)
                .recordTime(recordTime)
                .build();
        
        domainDataList = Arrays.asList(domainData);
    }

    @Test
    void testHandleDomainControllerAlarm_ShouldGenerateAlarm_WhenThresholdExceeded() {
        // Given
        Map<String, VehicleAlarmEventDTO> alarmMapDb = new HashMap<>();
        when(reportAlarmRepository.get(vehicleName)).thenReturn(alarmMapDb);
        
        // 模拟Redis List大小和数据
        when(redissonRepository.getListSize(anyString())).thenReturn(3);
        when(redissonRepository.getListRange(anyString(), eq(-3), eq(-1)))
                .thenReturn(Arrays.asList(120.0f, 121.0f, 122.0f)); // SOC温度连续超过阈值
        
        // When
        alarmManager.handleDomainControllerAlarm(vehicleName, domainDataList, recordTime);
        
        // Then
        verify(vehicleAlarmManager, times(1)).addAlarm(eq(vehicleName), any(VehicleAlarmEventDTO.class));
        verify(redissonRepository, atLeastOnce()).addToList(anyString(), anyFloat());
    }

    @Test
    void testHandleDomainControllerAlarm_ShouldNotGenerateAlarm_WhenAlarmAlreadyExists() {
        // Given
        Map<String, VehicleAlarmEventDTO> alarmMapDb = new HashMap<>();
        VehicleAlarmEventDTO existingAlarm = new VehicleAlarmEventDTO();
        existingAlarm.setType(AlarmTypeEnum.DOMAIN_CONTROLLER_ALARM.getValue());
        alarmMapDb.put(AlarmTypeEnum.DOMAIN_CONTROLLER_ALARM.getValue(), existingAlarm);
        
        when(reportAlarmRepository.get(vehicleName)).thenReturn(alarmMapDb);
        when(redissonRepository.getListSize(anyString())).thenReturn(3);
        when(redissonRepository.getListRange(anyString(), eq(-3), eq(-1)))
                .thenReturn(Arrays.asList(120.0f, 121.0f, 122.0f));
        
        // When
        alarmManager.handleDomainControllerAlarm(vehicleName, domainDataList, recordTime);
        
        // Then
        verify(vehicleAlarmManager, never()).addAlarm(eq(vehicleName), any(VehicleAlarmEventDTO.class));
    }

    @Test
    void testHandleDomainControllerAlarm_ShouldRemoveAlarm_WhenConditionsNormal() {
        // Given
        Map<String, VehicleAlarmEventDTO> alarmMapDb = new HashMap<>();
        VehicleAlarmEventDTO existingAlarm = new VehicleAlarmEventDTO();
        existingAlarm.setType(AlarmTypeEnum.DOMAIN_CONTROLLER_ALARM.getValue());
        alarmMapDb.put(AlarmTypeEnum.DOMAIN_CONTROLLER_ALARM.getValue(), existingAlarm);
        
        when(reportAlarmRepository.get(vehicleName)).thenReturn(alarmMapDb);
        
        // 模拟告警条件不满足（数据不足）
        when(redissonRepository.getListSize(anyString())).thenReturn(2); // 小于阈值3
        
        // 模拟消除条件满足
        when(redissonRepository.getListRange(anyString(), eq(-3), eq(-1)))
                .thenReturn(Arrays.asList(80.0f, 85.0f, 88.0f)); // 连续低于阈值
        
        // 创建正常的域控数据
        DomainDataDTO normalData = DomainDataDTO.builder()
                .vehicleName(vehicleName)
                .socTemperature(100.0f)  // 低于115度阈值
                .cpuLoad(80.0f)          // 低于90%阈值
                .memoryUsageRate(80.0f)  // 低于90%阈值
                .diskUsageRate(80.0f)    // 低于90%阈值
                .diskTemperature(60.0f)  // 低于70度阈值
                .deviceId(1)
                .recordTime(recordTime)
                .build();
        
        List<DomainDataDTO> normalDataList = Arrays.asList(normalData);
        
        // When
        alarmManager.handleDomainControllerAlarm(vehicleName, normalDataList, recordTime);
        
        // Then
        verify(vehicleAlarmManager, times(1)).removeAlarm(vehicleName, AlarmTypeEnum.DOMAIN_CONTROLLER_ALARM.getValue());
    }

    @Test
    void testHandleDomainControllerAlarm_ShouldNotGenerateAlarm_WhenThresholdNotExceeded() {
        // Given
        Map<String, VehicleAlarmEventDTO> alarmMapDb = new HashMap<>();
        when(reportAlarmRepository.get(vehicleName)).thenReturn(alarmMapDb);
        
        // 模拟数据未连续超过阈值
        when(redissonRepository.getListSize(anyString())).thenReturn(3);
        when(redissonRepository.getListRange(anyString(), eq(-3), eq(-1)))
                .thenReturn(Arrays.asList(110.0f, 120.0f, 100.0f)); // 不是连续超过阈值
        
        // When
        alarmManager.handleDomainControllerAlarm(vehicleName, domainDataList, recordTime);
        
        // Then
        verify(vehicleAlarmManager, never()).addAlarm(eq(vehicleName), any(VehicleAlarmEventDTO.class));
    }

    @Test
    void testHandleDomainControllerAlarm_ShouldHandleEmptyDomainDataList() {
        // Given
        List<DomainDataDTO> emptyList = Collections.emptyList();
        
        // When
        alarmManager.handleDomainControllerAlarm(vehicleName, emptyList, recordTime);
        
        // Then
        verify(reportAlarmRepository, never()).get(anyString());
        verify(vehicleAlarmManager, never()).addAlarm(anyString(), any(VehicleAlarmEventDTO.class));
        verify(vehicleAlarmManager, never()).removeAlarm(anyString(), anyString());
    }

    @Test
    void testHandleDomainControllerAlarm_ShouldHandleNullValues() {
        // Given
        DomainDataDTO dataWithNulls = DomainDataDTO.builder()
                .vehicleName(vehicleName)
                .socTemperature(null)    // null值
                .cpuLoad(95.0f)
                .memoryUsageRate(null)   // null值
                .diskUsageRate(95.0f)
                .diskTemperature(null)   // null值
                .deviceId(1)
                .recordTime(recordTime)
                .build();
        
        List<DomainDataDTO> dataListWithNulls = Arrays.asList(dataWithNulls);
        Map<String, VehicleAlarmEventDTO> alarmMapDb = new HashMap<>();
        when(reportAlarmRepository.get(vehicleName)).thenReturn(alarmMapDb);
        
        // When
        alarmManager.handleDomainControllerAlarm(vehicleName, dataListWithNulls, recordTime);
        
        // Then
        // 应该正常处理，不抛出异常
        verify(redissonRepository, atLeastOnce()).addToList(anyString(), anyFloat());
    }

    @Test
    void testHandleDomainControllerAlarm_ShouldHandleMultipleDevices() {
        // Given
        DomainDataDTO device1Data = DomainDataDTO.builder()
                .vehicleName(vehicleName)
                .socTemperature(120.0f)
                .cpuLoad(95.0f)
                .memoryUsageRate(85.0f)
                .diskUsageRate(95.0f)
                .diskTemperature(75.0f)
                .deviceId(1)
                .recordTime(recordTime)
                .build();
        
        DomainDataDTO device2Data = DomainDataDTO.builder()
                .vehicleName(vehicleName)
                .socTemperature(118.0f)
                .cpuLoad(92.0f)
                .memoryUsageRate(88.0f)
                .diskUsageRate(93.0f)
                .diskTemperature(72.0f)
                .deviceId(2)
                .recordTime(recordTime)
                .build();
        
        List<DomainDataDTO> multiDeviceList = Arrays.asList(device1Data, device2Data);
        Map<String, VehicleAlarmEventDTO> alarmMapDb = new HashMap<>();
        when(reportAlarmRepository.get(vehicleName)).thenReturn(alarmMapDb);
        
        when(redissonRepository.getListSize(anyString())).thenReturn(3);
        when(redissonRepository.getListRange(anyString(), eq(-3), eq(-1)))
                .thenReturn(Arrays.asList(120.0f, 121.0f, 122.0f));
        
        // When
        alarmManager.handleDomainControllerAlarm(vehicleName, multiDeviceList, recordTime);
        
        // Then
        // 应该为每个设备的每个指标都记录数据
        verify(redissonRepository, atLeast(10)).addToList(anyString(), anyFloat()); // 2设备 * 5指标
    }
}
