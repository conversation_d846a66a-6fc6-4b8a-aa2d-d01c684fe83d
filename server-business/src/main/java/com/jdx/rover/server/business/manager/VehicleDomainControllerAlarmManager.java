/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.server.business.manager;

import cn.hutool.core.collection.CollUtil;
import com.jdx.rover.server.api.domain.dto.guardian.VehicleAlarmEventDTO;
import com.jdx.rover.server.api.domain.dto.hardware.data.DomainDataDTO;
import com.jdx.rover.server.api.domain.enums.guardian.AlarmTypeEnum;
import com.jdx.rover.server.api.domain.enums.hardware.DomainControllerAlarmTypeEnum;
import com.jdx.rover.server.business.manager.report.VehicleAlarmManager;
import com.jdx.rover.server.repository.redis.RedissonRepository;
import com.jdx.rover.server.repository.redis.guardian.ReportAlarmRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 车辆域控告警管理器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-03
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class VehicleDomainControllerAlarmManager {

    private final VehicleAlarmManager vehicleAlarmManager;
    private final ReportAlarmRepository reportAlarmRepository;
    private final RedissonRepository redissonRepository;

    /**
     * Redis List key前缀 - 域控数据记录
     */
    private static final String REDIS_KEY_DOMAIN_DATA = "domain_controller_data:";

    /**
     * 连续异常次数阈值（1分钟，假设每20秒上报一次数据）
     */
    private static final int ABNORMAL_COUNT_THRESHOLD = 3;

    /**
     * 连续正常次数阈值（用于消除告警，15秒，假设每5秒检查一次）
     */
    private static final int NORMAL_COUNT_THRESHOLD = 3;

    /**
     * List最大长度
     */
    private static final int MAX_LIST_SIZE = 10;

    /**
     * List过期时间（秒）
     */
    private static final long LIST_EXPIRE_TIME = 3600;

    /**
     * 处理域控告警
     *
     * @param vehicleName 车辆名称
     * @param domainDataList 域控数据列表
     * @param recordTime 记录时间
     */
    public void handleDomainControllerAlarm(String vehicleName, List<DomainDataDTO> domainDataList, Date recordTime) {
        if (CollectionUtils.isEmpty(domainDataList)) {
            return;
        }

        Map<String, VehicleAlarmEventDTO> alarmMapDb = reportAlarmRepository.get(vehicleName);
        VehicleAlarmEventDTO alarmDb = alarmMapDb.get(AlarmTypeEnum.DOMAIN_CONTROLLER_ALARM.getValue());
        
        boolean shouldGenerateAlarm = false;
        StringBuilder errorMessage = new StringBuilder();

        // 检查每个域控数据
        for (DomainDataDTO domainData : domainDataList) {
            // 检查每种告警类型
            for (DomainControllerAlarmTypeEnum typeEnum : DomainControllerAlarmTypeEnum.values()) {
                String redisKey = getRedisKey(vehicleName, typeEnum, domainData.getDeviceId());
                Float fieldValue = typeEnum.getFieldValue(
                    domainData.getSocTemperature(),
                    domainData.getCpuLoad(),
                    domainData.getMemoryUsageRate(),
                    domainData.getDiskUsageRate(),
                    domainData.getDiskTemperature()
                );
                
                // 记录当前值到Redis List
                recordValueToRedis(redisKey, fieldValue);
                
                // 检查是否满足告警条件
                if (checkAlarmCondition(redisKey, typeEnum.getThreshold())) {
                    shouldGenerateAlarm = true;
                    errorMessage.append("deviceId:").append(domainData.getDeviceId())
                        .append(typeEnum.getTitle())
                        .append("连续")
                        .append(ABNORMAL_COUNT_THRESHOLD)
                        .append("次大于等于")
                        .append(typeEnum.getThreshold())
                        .append(getUnit(typeEnum))
                        .append("；");
                    log.info("【域控告警】{} 设备{} {} 连续{}次大于等于{}{}", 
                            vehicleName, domainData.getDeviceId(), typeEnum.getTitle(), 
                            ABNORMAL_COUNT_THRESHOLD, typeEnum.getThreshold(), getUnit(typeEnum));
                }
            }
        }

        // 生成告警
        if (shouldGenerateAlarm && Objects.isNull(alarmDb)) {
            VehicleAlarmEventDTO alarm = new VehicleAlarmEventDTO();
            alarm.setType(AlarmTypeEnum.DOMAIN_CONTROLLER_ALARM.getValue());
            alarm.setReportTime(recordTime);
            alarm.setErrorCode(AlarmTypeEnum.DOMAIN_CONTROLLER_ALARM.getValue());
            alarm.setErrorMessage(errorMessage.toString());
            vehicleAlarmManager.addAlarm(vehicleName, alarm);
            log.info("【域控告警】生成域控告警，车辆：{}，原因：{}", vehicleName, errorMessage);
            return;
        }

        // 检查告警消除条件
        if (!Objects.isNull(alarmDb)) {
            boolean shouldRemoveAlarm = checkAlarmRemovalConditions(vehicleName, domainDataList);
            if (shouldRemoveAlarm) {
                vehicleAlarmManager.removeAlarm(vehicleName, AlarmTypeEnum.DOMAIN_CONTROLLER_ALARM.getValue());
                log.info("【域控告警】消除域控告警，车辆：{}", vehicleName);
            }
        }
    }

    /**
     * 检查告警消除条件
     *
     * @param vehicleName 车辆名称
     * @param domainDataList 域控数据列表
     * @return true-应该消除告警，false-不应该消除告警
     */
    private boolean checkAlarmRemovalConditions(String vehicleName, List<DomainDataDTO> domainDataList) {
        if (CollectionUtils.isEmpty(domainDataList)) {
            return false;
        }

        // 检查所有设备的所有告警类型是否都满足消除条件
        for (DomainDataDTO domainData : domainDataList) {
            for (DomainControllerAlarmTypeEnum typeEnum : DomainControllerAlarmTypeEnum.values()) {
                String redisKey = getRedisKey(vehicleName, typeEnum, domainData.getDeviceId());
                
                // 如果任何一个类型不满足消除条件，则不消除告警
                if (!checkRemovalCondition(redisKey, typeEnum.getThreshold())) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 检查单个告警类型的告警条件
     *
     * @param redisKey Redis键
     * @param threshold 阈值
     * @return true-满足告警条件，false-不满足告警条件
     */
    private boolean checkAlarmCondition(String redisKey, Float threshold) {
        int listSize = redissonRepository.getListSize(redisKey);

        if (listSize < ABNORMAL_COUNT_THRESHOLD) {
            return false;
        }

        // 检查最近N次是否都超过阈值
        List<Float> recentValues = redissonRepository.getListRange(redisKey, -ABNORMAL_COUNT_THRESHOLD, -1);
        for (Float value : recentValues) {
            if (value == null || value < threshold) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查单个告警类型的消除条件
     *
     * @param redisKey Redis键
     * @param threshold 阈值
     * @return true-满足消除条件，false-不满足消除条件
     */
    private boolean checkRemovalCondition(String redisKey, Float threshold) {
        int listSize = redissonRepository.getListSize(redisKey);

        if (listSize < NORMAL_COUNT_THRESHOLD) {
            return false;
        }

        // 检查最近N次是否都小于阈值
        List<Float> recentValues = redissonRepository.getListRange(redisKey, -NORMAL_COUNT_THRESHOLD, -1);
        for (Float value : recentValues) {
            if (value == null || value >= threshold) {
                return false;
            }
        }

        return true;
    }

    /**
     * 记录值到Redis List
     *
     * @param redisKey Redis键
     * @param value 值
     */
    private void recordValueToRedis(String redisKey, Float value) {
        if (value == null) {
            return;
        }

        // 添加新的记录
        redissonRepository.addToList(redisKey, value);

        // 保持List大小不超过最大限制
        while (redissonRepository.getListSize(redisKey) > MAX_LIST_SIZE) {
            redissonRepository.removeFromList(redisKey, 0);
        }

        // 设置过期时间
        redissonRepository.expireList(redisKey, LIST_EXPIRE_TIME);
    }

    /**
     * 获取Redis键
     *
     * @param vehicleName 车辆名称
     * @param typeEnum 告警类型
     * @param deviceId 设备ID
     * @return Redis键
     */
    private String getRedisKey(String vehicleName, DomainControllerAlarmTypeEnum typeEnum, Integer deviceId) {
        return REDIS_KEY_DOMAIN_DATA + vehicleName + ":" + deviceId + ":" + typeEnum.getValue();
    }

    /**
     * 获取单位
     *
     * @param typeEnum 告警类型
     * @return 单位
     */
    private String getUnit(DomainControllerAlarmTypeEnum typeEnum) {
        return switch (typeEnum) {
            case SOC_TEMPERATURE, DISK_TEMPERATURE -> "度";
            case CPU_LOAD, MEMORY_USAGE, DISK_USAGE -> "%";
        };
    }
}
