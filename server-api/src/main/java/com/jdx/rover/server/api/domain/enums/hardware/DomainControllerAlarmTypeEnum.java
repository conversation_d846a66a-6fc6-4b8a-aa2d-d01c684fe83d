/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.server.api.domain.enums.hardware;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 域控告警类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-03
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum DomainControllerAlarmTypeEnum {
    /**
     * SOC温度告警
     * 持续1min，SOC温度大于等于115度
     */
    SOC_TEMPERATURE("SOC_TEMPERATURE", "SOC温度", 115.0f),
    
    /**
     * CPU负载率告警
     * 持续1min，CPU负载率大于等于90%
     */
    CPU_LOAD("CPU_LOAD", "CPU负载率", 90.0f),
    
    /**
     * 内存占用告警
     * 持续1min，内存占用大于等于90%
     */
    MEMORY_USAGE("MEMORY_USAGE", "内存占用", 90.0f),
    
    /**
     * 硬盘占用率告警
     * 持续1min，硬盘占用率大于等于90%
     */
    DISK_USAGE("DISK_USAGE", "硬盘占用率", 90.0f),
    
    /**
     * 硬盘温度告警
     * 持续1min，硬盘温度大于等于70度
     */
    DISK_TEMPERATURE("DISK_TEMPERATURE", "硬盘温度", 70.0f);

    /**
     * 告警类型值
     */
    private final String value;
    
    /**
     * 标题描述
     */
    private final String title;
    
    /**
     * 告警阈值
     */
    private final Float threshold;
    
    /**
     * 获取对应的域控数据字段值
     *
     * @param socTemperature SOC温度
     * @param cpuLoad CPU负载率
     * @param memoryUsageRate 内存占用率
     * @param diskUsageRate 硬盘占用率
     * @param diskTemperature 硬盘温度
     * @return 对应字段的值
     */
    public Float getFieldValue(Float socTemperature, Float cpuLoad, Float memoryUsageRate,
                              Float diskUsageRate, Float diskTemperature) {
        switch (this) {
            case SOC_TEMPERATURE:
                return socTemperature;
            case CPU_LOAD:
                return cpuLoad;
            case MEMORY_USAGE:
                return memoryUsageRate;
            case DISK_USAGE:
                return diskUsageRate;
            case DISK_TEMPERATURE:
                return diskTemperature;
            default:
                return null;
        }
    }
}
