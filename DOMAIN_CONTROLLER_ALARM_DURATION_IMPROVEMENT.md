# 域控告警持续时间改进

## 改进概述

本次改进主要针对域控告警功能，实现了基于真实时间持续性的告警判断机制。之前的实现只是简单地检查最近N条记录是否都超过阈值，现在改为检查数据是否在指定的时间段内持续超过阈值，这样更符合实际的业务需求。

## 问题分析

### 原有问题
1. **不准确的持续时间计算**：原来只检查连续N次记录，没有考虑记录之间的时间间隔
2. **忽略数据上报频率**：如果数据上报频率不稳定，可能导致误报或漏报
3. **无法处理间歇性异常**：如果异常值之间有正常值，无法正确判断持续时间

### 业务需求
- SOC温度、CPU负载率、内存占用率、硬盘占用率、硬盘温度需要**持续1分钟**超过阈值才告警
- 告警消除需要**持续15秒**正常才能消除

## 主要变更

### 1. 修改阈值常量

```java
// 修改前：基于次数
private static final int ABNORMAL_COUNT_THRESHOLD = 3;
private static final int NORMAL_COUNT_THRESHOLD = 3;

// 修改后：基于时间
private static final long ABNORMAL_DURATION_THRESHOLD_MS = 60 * 1000L;  // 1分钟
private static final long NORMAL_DURATION_THRESHOLD_MS = 15 * 1000L;    // 15秒
```

### 2. 增强DomainDataRecord类

添加了时间相关的工具方法：

```java
public long getTimeDifferenceMs(DomainDataRecord other) {
    if (this.recordTime == null || other == null || other.recordTime == null) {
        return -1;
    }
    return Math.abs(this.recordTime.getTime() - other.recordTime.getTime());
}

public boolean isAfter(Date time) {
    return recordTime != null && time != null && recordTime.after(time);
}

public boolean isBefore(Date time) {
    return recordTime != null && time != null && recordTime.before(time);
}
```

### 3. 重写告警条件检查方法

#### checkAlarmCondition方法

```java
private boolean checkAlarmCondition(String redisKey, Float threshold) {
    // 获取所有记录
    List<DomainDataRecord> allRecords = redissonRepository.getListRange(redisKey, 0, -1);
    
    // 检查是否存在持续超过阈值的时间段
    return checkContinuousAbnormalDuration(allRecords, threshold, ABNORMAL_DURATION_THRESHOLD_MS);
}
```

#### checkContinuousAbnormalDuration方法

```java
private boolean checkContinuousAbnormalDuration(List<DomainDataRecord> records, Float threshold, long durationThresholdMs) {
    // 按时间排序
    records.sort((r1, r2) -> r1.getRecordTime().compareTo(r2.getRecordTime()));

    Date abnormalStartTime = null;
    
    for (DomainDataRecord record : records) {
        if (record.isAboveThreshold(threshold)) {
            // 当前值超过阈值
            if (abnormalStartTime == null) {
                abnormalStartTime = record.getRecordTime();
            } else {
                // 检查持续时间
                long duration = record.getRecordTime().getTime() - abnormalStartTime.getTime();
                if (duration >= durationThresholdMs) {
                    return true;  // 找到持续超过阈值的时间段
                }
            }
        } else {
            // 当前值正常，重置异常开始时间
            abnormalStartTime = null;
        }
    }
    
    return false;
}
```

### 4. 重写告警消除条件检查

#### checkRemovalCondition方法

```java
private boolean checkRemovalCondition(String redisKey, Float threshold) {
    List<DomainDataRecord> allRecords = redissonRepository.getListRange(redisKey, 0, -1);
    return checkContinuousNormalDuration(allRecords, threshold, NORMAL_DURATION_THRESHOLD_MS);
}
```

#### checkContinuousNormalDuration方法

```java
private boolean checkContinuousNormalDuration(List<DomainDataRecord> records, Float threshold, long durationThresholdMs) {
    records.sort((r1, r2) -> r1.getRecordTime().compareTo(r2.getRecordTime()));

    Date normalStartTime = null;
    
    for (DomainDataRecord record : records) {
        if (record.isBelowThreshold(threshold)) {
            // 当前值正常
            if (normalStartTime == null) {
                normalStartTime = record.getRecordTime();
            } else {
                // 检查持续时间
                long duration = record.getRecordTime().getTime() - normalStartTime.getTime();
                if (duration >= durationThresholdMs) {
                    return true;  // 找到持续正常的时间段
                }
            }
        } else {
            // 当前值异常，重置正常开始时间
            normalStartTime = null;
        }
    }
    
    return false;
}
```

### 5. 更新告警消息

```java
// 修改前
errorMessage.append("连续").append(ABNORMAL_COUNT_THRESHOLD).append("次大于等于")

// 修改后
errorMessage.append("持续").append(ABNORMAL_DURATION_THRESHOLD_MS / 1000).append("秒大于等于")
```

## 改进效果

### 1. 精确的时间控制
- **之前**：基于记录次数，无法准确控制时间
- **现在**：基于真实的recordTime，精确控制持续时间

### 2. 处理数据上报频率变化
- **之前**：假设固定的上报频率，实际可能不准确
- **现在**：不依赖上报频率，只关注实际的时间跨度

### 3. 正确处理间歇性异常
- **之前**：连续N次超过阈值就告警，可能误报
- **现在**：必须在指定时间段内持续超过阈值，避免间歇性异常的误报

### 4. 灵活的时间配置
- **之前**：硬编码的次数阈值
- **现在**：可配置的时间阈值，更容易调整

## 测试用例

### 1. 持续时间超过阈值的测试

```java
@Test
void testHandleDomainControllerAlarm_ShouldGenerateAlarm_WhenDurationExceeded() {
    // 创建持续超过阈值的记录（持续时间超过60秒）
    Date baseTime = new Date();
    List<DomainDataRecord> exceedingRecords = Arrays.asList(
        DomainDataRecord.of(120.0f, new Date(baseTime.getTime())),
        DomainDataRecord.of(121.0f, new Date(baseTime.getTime() + 30000)), // 30秒后
        DomainDataRecord.of(122.0f, new Date(baseTime.getTime() + 65000))  // 65秒后，超过60秒阈值
    );
    
    // 验证生成告警
    verify(vehicleAlarmManager, times(1)).addAlarm(eq(vehicleName), any(VehicleAlarmEventDTO.class));
}
```

### 2. 间歇性异常不告警的测试

```java
@Test
void testHandleDomainControllerAlarm_ShouldNotGenerateAlarm_WhenValuesIntermittent() {
    // 创建间歇性超过阈值的记录（中间有正常值）
    Date baseTime = new Date();
    List<DomainDataRecord> intermittentRecords = Arrays.asList(
        DomainDataRecord.of(120.0f, new Date(baseTime.getTime())),
        DomainDataRecord.of(110.0f, new Date(baseTime.getTime() + 30000)), // 30秒后，正常值
        DomainDataRecord.of(125.0f, new Date(baseTime.getTime() + 60000)), // 60秒后，异常值
        DomainDataRecord.of(130.0f, new Date(baseTime.getTime() + 90000))  // 90秒后，异常值
    );
    
    // 验证不生成告警
    verify(vehicleAlarmManager, never()).addAlarm(eq(vehicleName), any(VehicleAlarmEventDTO.class));
}
```

## 使用示例

### 场景1：正常告警流程
1. 车辆上报SOC温度120度（超过115度阈值）
2. 30秒后上报SOC温度121度（仍超过阈值）
3. 65秒后上报SOC温度122度（持续时间超过60秒）
4. **触发告警**：SOC温度持续60秒大于等于115度

### 场景2：间歇性异常不告警
1. 车辆上报SOC温度120度（超过阈值）
2. 30秒后上报SOC温度110度（正常值，重置计时）
3. 60秒后上报SOC温度125度（重新开始计时）
4. **不触发告警**：没有持续60秒超过阈值

### 场景3：告警消除
1. 存在域控告警
2. 车辆上报正常数据（低于阈值）
3. 持续15秒都是正常数据
4. **消除告警**：持续15秒正常

## 兼容性说明

- 此改进保持了API接口不变
- Redis数据结构保持DomainDataRecord格式
- 配置参数从次数改为时间，需要更新相关文档

## 总结

通过这次改进，域控告警系统现在能够：
1. **精确控制持续时间**：基于真实的recordTime计算持续时间
2. **避免误报**：正确处理间歇性异常，只有真正持续的异常才告警
3. **灵活配置**：可以根据业务需求调整时间阈值
4. **提高准确性**：不依赖数据上报频率，更加可靠

这个改进使得告警系统更加符合实际的业务需求，提高了告警的准确性和可靠性。
